#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
房间识别处理器
功能：
1. 建筑外轮廓识别
2. 房间识别（客厅、卧室、阳台、厨房、卫生间、杂物间、其他房间、设备平台）
3. 房间切分功能
4. 房间类型修改

依据：已识别的墙体组和门窗栏杆组
- 通过墙体和门窗组识别建筑外轮廓
- 通过墙体间的门窗来切分房间
"""

import numpy as np
from shapely.geometry import Polygon, Point, LineString, MultiPolygon
from shapely.ops import unary_union, polygonize
import matplotlib.pyplot as plt
from matplotlib.patches import Polygon as MplPolygon
import tkinter as tk
from tkinter import ttk, messagebox


class RoomRecognitionProcessor:
    """房间识别处理器"""
    
    def __init__(self):
        """初始化房间识别处理器"""
        self.rooms = []  # 识别到的房间列表
        self.building_outline = None  # 建筑外轮廓
        self.wall_groups = []  # 墙体组
        self.door_window_groups = []  # 门窗组
        
        # 房间类型定义（增强版 - 包含新的分类）
        self.room_types = [
            '客厅', '卧室', '阳台', '厨房', '卫生间',
            '杂物间', '其他房间', '设备平台', '墙体空腔'
        ]

        # 房间类型颜色映射（增强版）
        self.room_colors = {
            '客厅': '#FFE4B5',      # 浅橙色
            '卧室': '#E6E6FA',      # 淡紫色
            '阳台': '#F0FFF0',      # 蜜瓜色
            '厨房': '#FFF8DC',      # 玉米丝色
            '卫生间': '#E0FFFF',    # 浅青色
            '杂物间': '#F5F5DC',    # 米色
            '其他房间': '#F0F8FF',  # 爱丽丝蓝
            '墙体空腔': '#D3D3D3',  # 浅灰色
            '设备平台': '#F5F5F5'   # 白烟色
        }
    
    def process_room_recognition(self, wall_groups, door_window_groups):
        """
        处理房间识别
        
        Args:
            wall_groups: 墙体组列表
            door_window_groups: 门窗组列表
            
        Returns:
            dict: 包含建筑外轮廓和房间信息的字典
        """
        try:
            print("🏠 开始房间识别处理...")
            
            self.wall_groups = wall_groups
            self.door_window_groups = door_window_groups
            
            # 1. 识别建筑外轮廓
            self.building_outline = self._identify_building_outline()
            
            # 2. 识别房间
            self.rooms = self._identify_rooms()
            
            # 3. 自动分类房间
            self._classify_rooms_automatically()
            
            result = {
                'building_outline': self.building_outline,
                'rooms': self.rooms,
                'wall_groups': self.wall_groups,
                'door_window_groups': self.door_window_groups
            }
            
            print(f"✅ 房间识别完成：建筑外轮廓 {'已识别' if self.building_outline else '未识别'}，房间数量 {len(self.rooms)}")
            
            return result
            
        except Exception as e:
            print(f"❌ 房间识别处理失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _identify_building_outline(self):
        """识别建筑外轮廓"""
        try:
            print("🔍 识别建筑外轮廓...")
            
            if not self.wall_groups:
                print("❌ 没有墙体组，无法识别建筑外轮廓")
                return None
            
            # 收集所有墙体线段
            all_wall_lines = []
            for group in self.wall_groups:
                for entity in group:
                    if entity.get('type') in ['LINE', 'LWPOLYLINE', 'POLYLINE']:
                        line_coords = self._extract_line_coordinates(entity)
                        if line_coords:
                            all_wall_lines.extend(line_coords)
            
            if not all_wall_lines:
                print("❌ 没有有效的墙体线段")
                return None
            
            # 创建线段几何对象
            line_geometries = []
            for line_coord in all_wall_lines:
                if len(line_coord) >= 2:
                    line_geometries.append(LineString(line_coord))
            
            if not line_geometries:
                print("❌ 没有有效的线段几何对象")
                return None
            
            # 使用polygonize创建多边形
            polygons = list(polygonize(line_geometries))
            
            if not polygons:
                print("❌ 无法从墙体线段创建多边形")
                return None
            
            # 选择最大的多边形作为建筑外轮廓
            largest_polygon = max(polygons, key=lambda p: p.area)
            
            print(f"✅ 识别到建筑外轮廓，面积: {largest_polygon.area:.2f}")
            
            return largest_polygon
            
        except Exception as e:
            print(f"❌ 识别建筑外轮廓失败: {e}")
            return None
    
    def _identify_rooms(self):
        """识别房间（增强版 - 实现详细的房间划分逻辑）"""
        try:
            print("🏠 开始详细房间识别...")

            if not self.wall_groups:
                print("❌ 没有墙体组，无法识别房间")
                return []

            # 步骤1: 检测门窗与墙体交点，生成临时线条
            print("🔍 步骤1: 检测门窗与墙体交点...")
            temp_lines = self._detect_door_window_wall_intersections()

            # 步骤2: 收集所有墙体实体和临时线条
            print("🔍 步骤2: 收集墙体实体和临时线条...")
            all_line_geometries = self._collect_all_line_geometries(temp_lines)

            if not all_line_geometries:
                print("❌ 没有有效的线段用于房间识别")
                return []

            # 步骤3: 检测围合区域
            print("🔍 步骤3: 检测围合区域...")
            all_polygons = list(polygonize(all_line_geometries))

            if not all_polygons:
                print("❌ 无法生成围合区域")
                return []

            print(f"✅ 检测到 {len(all_polygons)} 个围合区域")

            # 步骤4: 区域分组和分类
            print("🔍 步骤4: 区域分组和分类...")
            rooms = self._classify_regions(all_polygons)

            print(f"✅ 识别到 {len(rooms)} 个房间")

            return rooms
            
            print(f"✅ 识别到 {len(rooms)} 个房间")
            
            return rooms

        except Exception as e:
            print(f"❌ 识别房间失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def _detect_door_window_wall_intersections(self):
        """检测门窗与墙体交点，生成临时线条"""
        temp_lines = []
        tolerance = 20  # 容差
        min_distance = 300  # 最小距离
        alignment_tolerance = 10  # 水平/垂直对齐容差
        large_distance_threshold = 400  # 大距离阈值

        try:
            print(f"🔍 检测门窗与墙体交点（容差: {tolerance}）...")

            # 收集所有墙体线段
            wall_lines = []
            for group in self.wall_groups:
                for entity in group:
                    line_coords = self._extract_line_coordinates(entity)
                    if line_coords:
                        for coord in line_coords:
                            if len(coord) >= 2:
                                wall_lines.append(LineString(coord))

            print(f"✅ 收集到 {len(wall_lines)} 条墙体线段")

            # 检测每个门窗组
            for group_idx, door_window_group in enumerate(self.door_window_groups):
                print(f"🚪 检测门窗组 {group_idx + 1}...")

                # 收集门窗组的线段
                door_window_lines = []
                for entity in door_window_group:
                    line_coords = self._extract_line_coordinates(entity)
                    if line_coords:
                        for coord in line_coords:
                            if len(coord) >= 2:
                                door_window_lines.append(LineString(coord))

                if not door_window_lines:
                    continue

                # 检测与墙体的交点
                intersection_points = []
                for dw_line in door_window_lines:
                    for wall_line in wall_lines:
                        # 检测交点（使用缓冲区增加容差）
                        dw_buffered = dw_line.buffer(tolerance)
                        wall_buffered = wall_line.buffer(tolerance)

                        if dw_buffered.intersects(wall_buffered):
                            intersection = dw_buffered.intersection(wall_buffered)

                            # 提取交点坐标
                            if hasattr(intersection, 'centroid'):
                                point = intersection.centroid
                                intersection_points.append((point.x, point.y))

                # 去重交点（距离小于容差的点合并）
                unique_points = self._merge_close_points(intersection_points, tolerance)

                if len(unique_points) >= 2:
                    print(f"   ✅ 找到 {len(unique_points)} 个交点")

                    # 检查交点是否在水平或垂直线上
                    temp_lines_from_group = self._create_temp_lines_from_intersections(
                        unique_points, door_window_group, min_distance,
                        alignment_tolerance, large_distance_threshold
                    )

                    temp_lines.extend(temp_lines_from_group)
                else:
                    print(f"   ⚠️ 交点不足（{len(unique_points)}个），跳过")

            print(f"✅ 生成 {len(temp_lines)} 条临时线条")
            return temp_lines

        except Exception as e:
            print(f"❌ 检测门窗与墙体交点失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def _merge_close_points(self, points, tolerance):
        """合并距离小于容差的点"""
        if not points:
            return []

        unique_points = []
        for point in points:
            is_duplicate = False
            for existing_point in unique_points:
                distance = np.sqrt((point[0] - existing_point[0])**2 + (point[1] - existing_point[1])**2)
                if distance < tolerance:
                    is_duplicate = True
                    break

            if not is_duplicate:
                unique_points.append(point)

        return unique_points

    def _create_temp_lines_from_intersections(self, points, door_window_group,
                                            min_distance, alignment_tolerance, large_distance_threshold):
        """从交点创建临时线条"""
        temp_lines = []

        try:
            # 检查点是否在水平或垂直线上
            for i in range(len(points)):
                for j in range(i + 1, len(points)):
                    p1, p2 = points[i], points[j]
                    distance = np.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)

                    if distance > min_distance:
                        # 检查是否水平对齐
                        if abs(p1[1] - p2[1]) <= alignment_tolerance:
                            print(f"   🔗 创建水平临时线条: {p1} -> {p2}")
                            temp_lines.append(LineString([p1, p2]))

                        # 检查是否垂直对齐
                        elif abs(p1[0] - p2[0]) <= alignment_tolerance:
                            print(f"   🔗 创建垂直临时线条: {p1} -> {p2}")
                            temp_lines.append(LineString([p1, p2]))

                        # 如果距离很大，复制门窗组线条
                        elif distance >= large_distance_threshold:
                            print(f"   📋 距离较大({distance:.1f})，复制门窗组线条")
                            for entity in door_window_group:
                                line_coords = self._extract_line_coordinates(entity)
                                if line_coords:
                                    for coord in line_coords:
                                        if len(coord) >= 2:
                                            temp_lines.append(LineString(coord))

            return temp_lines

        except Exception as e:
            print(f"❌ 创建临时线条失败: {e}")
            return []

    def _collect_all_line_geometries(self, temp_lines):
        """收集所有墙体实体和临时线条"""
        all_line_geometries = []

        try:
            # 添加墙体线段
            wall_count = 0
            print(f"🔍 开始收集墙体线段，共 {len(self.wall_groups)} 个墙体组...")

            for group_idx, group in enumerate(self.wall_groups):
                print(f"   处理墙体组 {group_idx+1}: {len(group)} 个实体")

                for entity_idx, entity in enumerate(group):
                    # 调试：显示实体结构
                    entity_type = entity.get('type', 'UNKNOWN')
                    print(f"      实体 {entity_idx+1}: 类型={entity_type}")

                    # 显示实体的关键字段
                    if entity_type == 'LINE':
                        start = entity.get('start')
                        end = entity.get('end')
                        print(f"         start={start}, end={end}")
                    elif entity_type in ['LWPOLYLINE', 'POLYLINE']:
                        points = entity.get('points', [])
                        print(f"         points数量={len(points)}")
                    else:
                        # 显示实体的所有键
                        keys = list(entity.keys())
                        print(f"         可用键: {keys[:10]}...")  # 只显示前10个键

                    line_coords = self._extract_line_coordinates(entity)
                    if line_coords:
                        print(f"         ✅ 提取到 {len(line_coords)} 条线段")
                        for coord in line_coords:
                            if len(coord) >= 2:
                                all_line_geometries.append(LineString(coord))
                                wall_count += 1
                    else:
                        print(f"         ❌ 未能提取线段坐标")

            print(f"✅ 收集到 {wall_count} 条墙体线段")

            # 添加临时线条
            temp_count = len(temp_lines)
            all_line_geometries.extend(temp_lines)

            print(f"✅ 添加 {temp_count} 条临时线条")
            print(f"✅ 总计 {len(all_line_geometries)} 条线段用于围合")

            return all_line_geometries

        except Exception as e:
            print(f"❌ 收集线段失败: {e}")
            return []

    def _classify_regions(self, all_polygons):
        """区域分组和分类"""
        rooms = []

        try:
            print(f"🏷️ 开始区域分类...")

            # 过滤有效多边形
            valid_polygons = []
            for polygon in all_polygons:
                if polygon.is_valid and polygon.area > 50:  # 最小面积阈值
                    valid_polygons.append(polygon)

            print(f"✅ 有效区域: {len(valid_polygons)} 个")

            if not valid_polygons:
                return []

            # 步骤3: 宽度小于400的区域合并为一个区域
            narrow_regions, wide_regions = self._separate_narrow_wide_regions(valid_polygons, 400)

            # 合并窄区域
            if narrow_regions:
                merged_narrow = self._merge_narrow_regions(narrow_regions)
                if merged_narrow:
                    room = {
                        'geometry': merged_narrow,
                        'type': '墙体空腔',
                        'area': merged_narrow.area,
                        'centroid': merged_narrow.centroid,
                        'width': self._calculate_min_width(merged_narrow)
                    }
                    rooms.append(room)
                    print(f"✅ 合并窄区域: 面积 {merged_narrow.area:.1f}, 宽度 {room['width']:.1f}")

            # 步骤4-9: 分类宽区域
            for polygon in wide_regions:
                room = self._classify_single_region(polygon)
                rooms.append(room)

            # 步骤5: 面积最大的标记为客厅
            if rooms:
                largest_room = max(rooms, key=lambda r: r['area'])
                if largest_room['type'] != '墙体空腔':  # 空腔不能是客厅
                    largest_room['type'] = '客厅'
                    print(f"✅ 最大区域标记为客厅: 面积 {largest_room['area']:.1f}")

            # 输出分类结果
            type_counts = {}
            for room in rooms:
                room_type = room['type']
                type_counts[room_type] = type_counts.get(room_type, 0) + 1

            print("📊 房间分类结果:")
            for room_type, count in type_counts.items():
                print(f"   {room_type}: {count} 个")

            return rooms

        except Exception as e:
            print(f"❌ 区域分类失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def _separate_narrow_wide_regions(self, polygons, width_threshold):
        """分离窄区域和宽区域"""
        narrow_regions = []
        wide_regions = []

        for polygon in polygons:
            min_width = self._calculate_min_width(polygon)
            if min_width < width_threshold:
                narrow_regions.append(polygon)
            else:
                wide_regions.append(polygon)

        print(f"✅ 窄区域({width_threshold}以下): {len(narrow_regions)} 个")
        print(f"✅ 宽区域({width_threshold}以上): {len(wide_regions)} 个")

        return narrow_regions, wide_regions

    def _calculate_min_width(self, polygon):
        """计算多边形的最小宽度（短边）"""
        try:
            # 获取边界框
            minx, miny, maxx, maxy = polygon.bounds

            # 计算宽度和高度
            width = maxx - minx
            height = maxy - miny

            # 返回较小的值作为最小宽度
            return min(width, height)

        except Exception as e:
            print(f"❌ 计算最小宽度失败: {e}")
            return 0

    def _merge_narrow_regions(self, narrow_regions):
        """合并窄区域"""
        try:
            if not narrow_regions:
                return None

            if len(narrow_regions) == 1:
                return narrow_regions[0]

            # 使用unary_union合并所有窄区域
            merged = unary_union(narrow_regions)

            # 如果结果是MultiPolygon，选择最大的部分
            if isinstance(merged, MultiPolygon):
                merged = max(merged.geoms, key=lambda p: p.area)

            return merged

        except Exception as e:
            print(f"❌ 合并窄区域失败: {e}")
            return None

    def _classify_single_region(self, polygon):
        """分类单个区域"""
        try:
            area = polygon.area
            min_width = self._calculate_min_width(polygon)

            # 根据宽度（短边）进行分类
            if min_width > 400 and min_width < 1100:
                room_type = '设备平台'
            elif min_width >= 1100 and min_width < 1700:
                room_type = '阳台'
            elif min_width >= 1700 and min_width < 2500:
                room_type = '卫生间'
            else:
                room_type = '卧室'  # 其他标记为卧室

            room = {
                'geometry': polygon,
                'type': room_type,
                'area': area,
                'centroid': polygon.centroid,
                'width': min_width
            }

            print(f"✅ 区域分类: {room_type}, 面积: {area:.1f}, 宽度: {min_width:.1f}")

            return room

        except Exception as e:
            print(f"❌ 分类单个区域失败: {e}")
            return {
                'geometry': polygon,
                'type': '其他房间',
                'area': polygon.area if polygon else 0,
                'centroid': polygon.centroid if polygon else Point(0, 0),
                'width': 0
            }
    
    def _extract_line_coordinates(self, entity):
        """提取实体的线段坐标（改进版，支持多种格式）"""
        try:
            entity_type = entity.get('type', '')

            if entity_type == 'LINE':
                # 方法1: 标准结构 {start: {x, y}, end: {x, y}}
                start = entity.get('start', {})
                end = entity.get('end', {})
                print(f"            LINE实体: start={start}, end={end}")

                if start and end and isinstance(start, dict) and isinstance(end, dict):
                    start_x = start.get('x', 0)
                    start_y = start.get('y', 0)
                    end_x = end.get('x', 0)
                    end_y = end.get('y', 0)

                    coords = [(start_x, start_y), (end_x, end_y)]
                    print(f"            方法1成功: {coords}")
                    return [coords]

                # 方法2: 扁平化坐标 {start_x, start_y, end_x, end_y}
                if all(key in entity for key in ['start_x', 'start_y', 'end_x', 'end_y']):
                    coords = [(entity['start_x'], entity['start_y']),
                             (entity['end_x'], entity['end_y'])]
                    print(f"            方法2成功: {coords}")
                    return [coords]

                # 方法3: 坐标数组 {coordinates: [(x1,y1), (x2,y2)]}
                if 'coordinates' in entity:
                    coords = entity['coordinates']
                    if len(coords) >= 2:
                        print(f"            方法3成功: {coords}")
                        return [coords]

                # 方法4: 几何对象 {geometry: {coordinates: [[x1,y1], [x2,y2]]}}
                if 'geometry' in entity:
                    geom = entity['geometry']
                    if isinstance(geom, dict) and 'coordinates' in geom:
                        coords = geom['coordinates']
                        if len(coords) >= 2:
                            print(f"            方法4成功: {coords}")
                            return [coords]

                print(f"            ❌ 所有LINE提取方法都失败")

            elif entity_type in ['LWPOLYLINE', 'POLYLINE']:
                points = entity.get('points', [])
                print(f"            {entity_type}实体: {len(points)} 个点")

                if not points:
                    print(f"            ❌ 没有点数据")
                    return []

                coords = []

                # 方法1: 字典点 [{x, y}, {x, y}, ...]
                if isinstance(points[0], dict):
                    for i, p in enumerate(points):
                        if isinstance(p, dict):
                            x = p.get('x', 0)
                            y = p.get('y', 0)
                            coords.append((x, y))
                        else:
                            print(f"            ❌ 点 {i} 不是字典: {p}")
                    print(f"            字典点方法: 提取到 {len(coords)} 个坐标")

                # 方法2: 元组/列表点 [(x, y), (x, y), ...]
                elif isinstance(points[0], (tuple, list)):
                    coords = points
                    print(f"            元组点方法: 提取到 {len(coords)} 个坐标")

                else:
                    print(f"            ❌ 不支持的点格式: {type(points[0])}")
                    return []

                # 创建连续线段
                if len(coords) >= 2:
                    lines = []
                    for i in range(len(coords) - 1):
                        lines.append([coords[i], coords[i + 1]])
                    print(f"            ✅ 成功提取 {len(lines)} 条线段")
                    return lines
                else:
                    print(f"            ❌ 有效坐标不足: {len(coords)}")

            else:
                print(f"            ❌ 不支持的实体类型: {entity_type}")
                # 尝试查找其他可能的坐标字段
                possible_coord_keys = ['coordinates', 'geometry', 'vertices', 'start_point', 'end_point', 'points']
                found_keys = [key for key in possible_coord_keys if key in entity]
                if found_keys:
                    print(f"            发现可能的坐标字段: {found_keys}")
                    # 尝试通用坐标提取
                    for key in found_keys:
                        try:
                            coord_data = entity[key]
                            if isinstance(coord_data, list) and len(coord_data) >= 2:
                                print(f"            尝试使用 {key} 字段: {coord_data[:2]}...")
                                return [coord_data[:2]]
                        except:
                            continue

            print(f"            ❌ 所有提取方法都失败")
            return []

        except Exception as e:
            print(f"            ❌ 提取线段坐标异常: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    def _classify_rooms_automatically(self):
        """自动分类房间"""
        try:
            print("🤖 自动分类房间...")
            
            for room in self.rooms:
                area = room['area']
                
                # 基于面积的简单分类规则
                if area > 2000:
                    room['type'] = '客厅'
                elif area > 1000:
                    room['type'] = '卧室'
                elif area > 500:
                    if area < 800:
                        room['type'] = '卫生间'
                    else:
                        room['type'] = '厨房'
                elif area > 200:
                    room['type'] = '阳台'
                else:
                    room['type'] = '杂物间'
            
            print("✅ 房间自动分类完成")
            
        except Exception as e:
            print(f"❌ 房间自动分类失败: {e}")
    
    def update_room_type(self, room_index, new_type):
        """更新房间类型"""
        try:
            if 0 <= room_index < len(self.rooms):
                if new_type in self.room_types:
                    old_type = self.rooms[room_index]['type']
                    self.rooms[room_index]['type'] = new_type
                    print(f"✅ 房间 {room_index + 1} 类型已从 '{old_type}' 更改为 '{new_type}'")
                    return True
                else:
                    print(f"❌ 无效的房间类型: {new_type}")
                    return False
            else:
                print(f"❌ 无效的房间索引: {room_index}")
                return False
                
        except Exception as e:
            print(f"❌ 更新房间类型失败: {e}")
            return False
    
    def get_room_statistics(self):
        """获取房间统计信息"""
        try:
            stats = {}
            total_area = 0
            
            for room in self.rooms:
                room_type = room['type']
                area = room['area']
                
                if room_type not in stats:
                    stats[room_type] = {'count': 0, 'total_area': 0}
                
                stats[room_type]['count'] += 1
                stats[room_type]['total_area'] += area
                total_area += area
            
            # 添加总计信息
            stats['总计'] = {
                'count': len(self.rooms),
                'total_area': total_area
            }
            
            return stats
            
        except Exception as e:
            print(f"❌ 获取房间统计信息失败: {e}")
            return {}
    
    def export_room_data(self):
        """导出房间数据"""
        try:
            room_data = []
            
            for i, room in enumerate(self.rooms):
                data = {
                    'room_id': i + 1,
                    'type': room['type'],
                    'area': room['area'],
                    'centroid_x': room['centroid'].x,
                    'centroid_y': room['centroid'].y,
                    'color': self.room_colors.get(room['type'], '#F0F8FF')
                }
                room_data.append(data)
            
            return {
                'building_outline_area': self.building_outline.area if self.building_outline else 0,
                'total_rooms': len(self.rooms),
                'rooms': room_data,
                'statistics': self.get_room_statistics()
            }
            
        except Exception as e:
            print(f"❌ 导出房间数据失败: {e}")
            return None
